<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<!-- Plan Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ __('Plan Management') }}</h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary active" data-plan-view="products">
                    <i class="ti ti-box me-1"></i>{{ __('Products') }}
                </button>
                <button type="button" class="btn btn-outline-primary" data-plan-view="coupons">
                    <i class="ti ti-ticket me-1"></i>{{ __('Coupons') }}
                </button>
                <button type="button" class="btn btn-outline-primary" data-plan-view="links">
                    <i class="ti ti-link me-1"></i>{{ __('Links') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Products View -->
<div id="products-view" class="plan-view active">
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Products Table') }}</h5>
                    <div class="d-flex gap-2">
                        @can('create product & service')
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addProductModal">
                                <i class="ti ti-plus me-1"></i>{{ __('Add Product') }}
                            </button>
                        @endcan
                        <button class="btn btn-primary btn-sm">
                            <i class="ti ti-download me-1"></i>{{ __('Export') }}
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ti ti-settings me-1"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="ti ti-refresh me-2"></i>{{ __('Refresh') }}</a></li>
                                <li><a class="dropdown-item" href="#"><i class="ti ti-filter me-2"></i>{{ __('Filter') }}</a></li>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                {{ __('10') }}
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#">10</a></li>
                                <li><a class="dropdown-item" href="#">25</a></li>
                                <li><a class="dropdown-item" href="#">50</a></li>
                                <li><a class="dropdown-item" href="#">100</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 50px;">#</th>
                                    <th>{{ __('Name') }} <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th>{{ __('Image') }}</th>
                                    <th>{{ __('Price') }} <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th>{{ __('Down Payment') }} <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th>{{ __('Tax') }} <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th>{{ __('Total Sales') }} <i class="ti ti-chevron-up text-muted"></i></th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    try {
                                        $products = \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())->limit(5)->get();
                                    } catch (\Exception $e) {
                                        $products = collect([]);
                                    }
                                @endphp
                                @forelse($products as $index => $product)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="{{ $product->name }}" readonly>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            <i class="ti ti-package text-muted"></i>
                                        </div>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="{{ \Auth::user()->priceFormat($product->sale_price) }}" readonly>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="0.00" readonly>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="{{ $product->tax ? $product->tax->rate : '0.00' }}" readonly>
                                    </td>
                                    <td>
                                        <input type="text" class="form-control form-control-sm border-0 bg-transparent" value="0" readonly>
                                    </td>
                                    <td>
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" {{ $product->is_active ? 'checked' : '' }}>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                                                <i class="ti ti-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" title="{{ __('Edit') }}">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-info" title="{{ __('Duplicate') }}">
                                                <i class="ti ti-copy"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" title="{{ __('Share') }}">
                                                <i class="ti ti-share"></i>
                                            </button>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    <i class="ti ti-dots"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="{{ route('productservice.edit', $product->id) }}"><i class="ti ti-edit me-2"></i>{{ __('Edit') }}</a></li>
                                                    <li><a class="dropdown-item" href="#"><i class="ti ti-copy me-2"></i>{{ __('Duplicate') }}</a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#"><i class="ti ti-trash me-2"></i>{{ __('Delete') }}</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ti ti-package" style="font-size: 3rem;"></i>
                                            <p class="mt-2">{{ __('No products found') }}</p>
                                            @can('create product & service')
                                                <a href="{{ route('productservice.create') }}" class="btn btn-primary btn-sm">
                                                    <i class="ti ti-plus me-1"></i>{{ __('Add First Product') }}
                                                </a>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Coupons View -->
<div id="coupons-view" class="plan-view" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Coupons Management') }}</h5>
                    <div class="d-flex gap-2">
                        {{-- Temporarily show button for all users - remove @can check for testing --}}
                        {{-- @can('create coupon') --}}
                            <button type="button" class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
                                <i class="ti ti-plus me-1"></i>{{ __('Create Coupon') }}
                            </button>
                        {{-- @endcan --}}
                        <button class="btn btn-primary btn-sm">
                            <i class="ti ti-download me-1"></i>{{ __('Export') }}
                        </button>
                        <div class="dropdown">
                            <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="ti ti-settings me-1"></i>
                            </button>
                            <!-- <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#"><i class="ti ti-refresh me-2"></i>{{ __('Refresh') }}</a></li>
                                <li><a class="dropdown-item" href="#"><i class="ti ti-filter me-2"></i>{{ __('Filter') }}</a></li>
                            </ul> -->
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('Name') }}</th>
                                    <th>{{ __('Code') }}</th>
                                    <th>{{ __('Product') }}</th>
                                    <th>{{ __('Start`s From') }}</th>
                                    <th>{{ __('End`s at') }}</th>
                                    <th>{{ __('Amount') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    try {
                                        $coupons = \App\Models\Coupon::where('created_by', \Auth::user()->creatorId())->limit(5)->get();
                                    } catch (\Exception $e) {
                                        $coupons = collect([]);
                                    }
                                @endphp
                                @forelse($coupons as $coupon)
                                <tr>
                                    <td>{{ $coupon->name }}</td>
                                    <td><code>{{ $coupon->code }}</code></td>
                                    <td>{{ $coupon->product_name ?? __('All Products') }}</td>
                                    <td>{{ $coupon->start_date ? \Carbon\Carbon::parse($coupon->start_date)->format('M d, Y') : __('No Start Date') }}</td>
                                    <td>{{ $coupon->end_date ? \Carbon\Carbon::parse($coupon->end_date)->format('M d, Y') : __('No End Date') }}</td>
                                    <td>{{ $coupon->discount }}{{ ($coupon->discount_type ?? 'percentage') == 'percentage' ? '%' : \Auth::user()->currencySymbol() }}</td>
                                    <td>
                                        <span class="badge bg-{{ $coupon->is_active ? 'success' : 'danger' }}">
                                            {{ $coupon->is_active ? __('Active') : __('Inactive') }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-sm btn-outline-primary" data-coupon-id="{{ $coupon->id }}" id="viewCoupon" title="{{ __('View') }}">
                                                <i class="ti ti-eye"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" data-coupon-id="{{ $coupon->id }}" id="editCoupon" title="{{ __('Edit') }}">
                                                <i class="ti ti-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" data-coupon-id="{{ $coupon->id }}" id="deleteCoupon" title="{{ __('Delete') }}">
                                                <i class="ti ti-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="ti ti-ticket" style="font-size: 3rem;"></i>
                                            <p class="mt-2">{{ __('No coupons found') }}</p>
                                            {{-- @can('create coupon') --}}
                                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addCouponModal">
                                                    <i class="ti ti-plus me-1"></i>{{ __('Add First Coupon') }}
                                                </button>
                                            {{-- @endcan --}}
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Links View -->
<div id="links-view" class="plan-view" style="display: none;">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Payment Links') }}</h5>
                    <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#createLinkModal">
                        <i class="ti ti-plus me-1"></i>{{ __('Create Link') }}
                    </button>
                </div>
                <div class="card-body">
                    <div class="text-center py-5">
                        <i class="ti ti-link text-muted" style="font-size: 4rem;"></i>
                        <h5 class="mt-3">{{ __('Payment Links') }}</h5>
                        <p class="text-muted">{{ __('Create shareable payment links for quick transactions') }}</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createLinkModal">
                            <i class="ti ti-plus me-1"></i>{{ __('Create Your First Link') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Plan view switching
    const planViewButtons = document.querySelectorAll('[data-plan-view]');
    const planViews = document.querySelectorAll('.plan-view');
    
    planViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-plan-view');
            
            // Remove active class from all buttons
            planViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Hide all views
            planViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });
});
</script>

<style>
#addProductModal .toolbar button {
    border: 1px solid #dee2e6;
    background: #f8f9fa;
    color: #495057;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

#addProductModal .toolbar button:hover {
    background: #e9ecef;
}

#addProductModal .form-select-sm {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
}

#addProductModal #imagePlaceholder {
    border: 2px dashed #dee2e6;
    background: #f8f9fa;
}

#addProductModal .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#addProductModal .modal-xl {
    max-width: 1200px;
}

@media (max-width: 1200px) {
    #addProductModal .modal-xl {
        max-width: 95%;
    }
}

#addCouponModal .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

#addCouponModal .form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

#addCouponModal .btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}
</style>

<!-- Add Product Modal -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">{{ __('Add Product') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addProductForm" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <!-- Name and Nickname -->
                        <div class="col-md-6 mb-3">
                            <label for="productName" class="form-label">{{ __('Name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="productName" name="name" placeholder="Product Name" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productNickname" class="form-label">{{ __('Nickname (For internal use only)') }}</label>
                            <input type="text" class="form-control" id="productNickname" name="nickname" placeholder="Product Nickname">
                        </div>

                        <!-- Checkboxes Row 1 -->
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="restrictOneTime" name="restrict_one_time">
                                <label class="form-check-label" for="restrictOneTime">
                                    {{ __('Restrict to one time?') }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isFreeTrial" name="is_free_trial"
                                       onchange="handleFreeTrialChange(this)">
                                <label class="form-check-label" for="isFreeTrial">
                                    {{ __('Is it a free trial?') }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isSubscription" name="is_subscription"
                                       onchange="handleSubscriptionChange(this)">
                                <label class="form-check-label" for="isSubscription">
                                    {{ __('Is it a subscription?') }}
                                </label>
                            </div>
                        </div>

                        <!-- Price Row -->
                        <div class="col-md-3 mb-3">
                            <label for="productPrice" class="form-label">{{ __('Price') }} <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" class="form-control" id="productPrice" name="price" placeholder="Product Price" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="strikedPrice" class="form-label">{{ __('Striked Price') }}</label>
                            <input type="number" step="0.01" class="form-control" id="strikedPrice" name="striked_price" placeholder="Striked Price">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="taxSlab" class="form-label">{{ __('Tax Slab') }}</label>
                            <select class="form-select" id="taxSlab" name="tax_slab">
                                <option value="">{{ __('Select') }}</option>
                                @if(isset($tax))
                                    @foreach($tax as $id => $name)
                                        <option value="{{ $id }}">{{ $name }}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="taxRate" class="form-label">{{ __('Tax') }}</label>
                            <input type="number" step="0.01" class="form-control" id="taxRate" name="tax_rate" placeholder="0">
                        </div>

                        <!-- Checkboxes Row 2 -->
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="emiOptions" name="emi_options">
                                <label class="form-check-label" for="emiOptions">
                                    {{ __('Emi Options') }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showShippingField" name="show_shipping_field">
                                <label class="form-check-label" for="showShippingField">
                                    {{ __('Show shipping field') }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="paymentGateway" name="payment_gateway">
                                <label class="form-check-label" for="paymentGateway">
                                    {{ __('Payment Gateway') }}
                                </label>
                            </div>
                        </div>

                        <!-- Bump up and Image -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">{{ __('Bump up') }}</label>
                            <div class="d-flex align-items-center">
                                <button type="button" class="btn btn-success btn-sm">
                                    <i class="ti ti-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="productImage" class="form-label">{{ __('Image') }}</label>
                            <input type="file" class="form-control" id="productImage" name="pro_image" accept="image/*">
                            <small class="text-muted">
                                {{ __('Supported formats are jpg, jpeg and png') }}
                                <a href="https://www.canva.com" target="_blank" class="text-primary">{{ __('Create Product Image from Canva') }}</a>
                            </small>
                            <div class="mt-2">
                                <img id="imagePreview" src="#" alt="Image Preview" style="max-width: 150px; max-height: 150px; display: none;" class="img-thumbnail">
                                <div id="imagePlaceholder" class="bg-light border rounded d-flex align-items-center justify-content-center" style="width: 150px; height: 100px;">
                                    <i class="ti ti-photo text-muted" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Skip GST and HSN/SAC -->
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="skipGstForm" name="skip_gst_form">
                                <label class="form-check-label" for="skipGstForm">
                                    {{ __('Skip GST form?') }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="hsnSacNo" class="form-label">{{ __('HSN/SAC No.') }}</label>
                            <input type="text" class="form-control" id="hsnSacNo" name="hsn_sac_no" placeholder="HSN/SAC No.">
                        </div>

                        <!-- Redirect URL -->
                        <div class="col-12 mb-3">
                            <label for="redirectUrl" class="form-label">{{ __('Redirect Url') }}</label>
                            <input type="url" class="form-control" id="redirectUrl" name="redirect_url" placeholder="Redirect Url">
                        </div>

                        <!-- Description -->
                        <div class="col-12 mb-3">
                            <label for="description" class="form-label">{{ __('Description') }}</label>
                            <div class="border rounded">
                                <div class="toolbar border-bottom p-2">
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><strong>B</strong></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><em>I</em></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><u>U</u></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">S</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">"</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">&lt;/&gt;</button>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-1">
                                        <option>Normal</option>
                                        <option>H1</option>
                                        <option>H2</option>
                                    </select>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-1">
                                        <option>Normal</option>
                                    </select>
                                    <select class="form-select form-select-sm d-inline-block w-auto">
                                        <option>Sans Serif</option>
                                    </select>
                                </div>
                                <textarea class="form-control border-0" id="description" name="description" rows="6" placeholder="Enter your content..." style="resize: none;"></textarea>
                            </div>
                        </div>

                        <!-- Invoice Footer -->
                        <div class="col-12 mb-3">
                            <label for="invoiceFooter" class="form-label">{{ __('Invoice Footer') }}</label>
                            <div class="border rounded">
                                <div class="toolbar border-bottom p-2">
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><strong>B</strong></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><em>I</em></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1"><u>U</u></button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">S</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">"</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary me-1">&lt;/&gt;</button>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-1">
                                        <option>Normal</option>
                                        <option>H1</option>
                                        <option>H2</option>
                                    </select>
                                    <select class="form-select form-select-sm d-inline-block w-auto me-1">
                                        <option>Normal</option>
                                    </select>
                                    <select class="form-select form-select-sm d-inline-block w-auto">
                                        <option>Sans Serif</option>
                                    </select>
                                </div>
                                <textarea class="form-control border-0" id="invoiceFooter" name="invoice_footer" rows="6" placeholder="Enter your bank details, policies or anything else" style="resize: none;"></textarea>
                            </div>
                        </div>

                        <!-- Trial Fields (shown when "Is it a free trial?" is checked) -->
                        <div id="trialFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="trialDurationType" class="form-label">{{ __('Trial Duration Type') }} <span class="text-danger">*</span></label>
                                    <select class="form-select" id="trialDurationType" name="trial_duration_type">
                                        <option value="">{{ __('Select') }}</option>
                                        <option value="days">{{ __('Days') }}</option>
                                        <option value="months">{{ __('Months') }}</option>
                                        <option value="years">{{ __('Years') }}</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <label for="trialDuration" class="form-label">{{ __('Trial Duration') }} <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="trialDuration" name="trial_duration" placeholder="Trial Duration">
                                    <small class="text-muted">{{ __('No of days, months or years of trial period') }}</small>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <label for="trialPrice" class="form-label">{{ __('Trial Price') }} <span class="text-danger">*</span></label>
                                    <input type="number" step="0.01" class="form-control" id="trialPrice" name="trial_price" value="0">
                                </div>
                                <div class="col-md-6"></div>
                            </div>
                        </div>

                        <!-- Subscription Fields (shown when "Is it a subscription?" is checked) -->
                        <div id="subscriptionFields" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isCancellable" name="is_cancellable">
                                        <label class="form-check-label" for="isCancellable">
                                            {{ __('Is Cancellable?') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <label for="billedEvery" class="form-label">{{ __('Billed Every') }} <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="billedEvery" name="billed_every" value="1">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <select class="form-select" name="billing_cycle_type">
                                        <option value="monthly">{{ __('Monthly') }}</option>
                                        <option value="yearly">{{ __('Yearly') }}</option>
                                        <option value="weekly">{{ __('Weekly') }}</option>
                                    </select>
                                </div>
                                <div class="col-md-6"></div>

                                <div class="col-md-6 mb-3">
                                    <label for="billingCycle" class="form-label">{{ __('Billing Cycle') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="billingCycle" name="billing_cycle_display" value="Forever" readonly>
                                </div>
                                <div class="col-md-6"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Submit') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Coupon Modal -->
<div class="modal fade" id="addCouponModal" tabindex="-1" aria-labelledby="addCouponModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCouponModalLabel">{{ __('Create Coupon') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addCouponForm">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <!-- Coupon Name -->
                        <div class="col-md-6 mb-3">
                            <label for="couponName" class="form-label">{{ __('Coupon Name') }} <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="couponName" name="name" placeholder="{{ __('Enter coupon name') }}" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Coupon Code -->
                        <div class="col-md-6 mb-3">
                            <label for="couponCode" class="form-label">{{ __('Coupon Code') }} <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="couponCode" name="code" placeholder="{{ __('Enter coupon code') }}" required>
                                <button type="button" class="btn btn-outline-secondary generate_coupon" id="generateCodeBtn">
                                    <i class="ti ti-refresh"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Product Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="couponProduct" class="form-label">{{ __('Product') }}</label>
                            <select class="form-select" id="couponProduct" name="product_id">
                                <option value="">{{ __('All Products') }}</option>
                                @php
                                    try {
                                        $products = \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())->get();
                                    } catch (\Exception $e) {
                                        $products = collect([]);
                                    }
                                @endphp
                                @foreach($products as $product)
                                    <option value="{{ $product->id }}">{{ $product->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Discount Type -->
                        <div class="col-md-6 mb-3">
                            <label for="discountType" class="form-label">{{ __('Discount Type') }} <span class="text-danger">*</span></label>
                            <select class="form-select" id="discountType" name="discount_type" required>
                                <option value="percentage">{{ __('Percentage') }}</option>
                                <option value="fixed">{{ __('Fixed Amount') }}</option>
                            </select>
                        </div>

                        <!-- Discount Amount -->
                        <div class="col-md-6 mb-3">
                            <label for="discountAmount" class="form-label">{{ __('Discount Amount') }} <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" class="form-control" id="discountAmount" name="discount" placeholder="{{ __('Enter discount amount') }}" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Usage Limit -->
                        <div class="col-md-6 mb-3">
                            <label for="usageLimit" class="form-label">{{ __('Usage Limit') }} <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="usageLimit" name="limit" placeholder="{{ __('Enter usage limit') }}" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <!-- Start Date -->
                        <div class="col-md-6 mb-3">
                            <label for="startDate" class="form-label">{{ __('Start Date') }}</label>
                            <input type="date" class="form-control" id="startDate" name="start_date">
                        </div>

                        <!-- End Date -->
                        <div class="col-md-6 mb-3">
                            <label for="endDate" class="form-label">{{ __('End Date') }}</label>
                            <input type="date" class="form-control" id="endDate" name="end_date">
                        </div>

                        <!-- Status -->
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                                <label class="form-check-label" for="isActive">
                                    {{ __('Active') }}
                                </label>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="col-12 mb-3">
                            <label for="couponDescription" class="form-label">{{ __('Description') }}</label>
                            <textarea class="form-control" id="couponDescription" name="description" rows="3" placeholder="{{ __('Enter coupon description (optional)') }}"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                    <button type="submit" class="btn btn-success">{{ __('Create Coupon') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Coupon Modal -->
<div class="modal fade" id="viewCouponModal" tabindex="-1" aria-labelledby="viewCouponModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewCouponModalLabel">{{ __('View Coupon') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Content will be populated by JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
            </div>
        </div>
    </div>
</div>

<script>
// Inline functions for immediate execution
function handleFreeTrialChange(checkbox) {
    console.log('Free trial changed (inline):', checkbox.checked);
    if (checkbox.checked) {
        document.getElementById('trialFields').style.display = 'block';
        document.getElementById('subscriptionFields').style.display = 'none';
        document.getElementById('isSubscription').checked = false;

        // Set required attributes
        document.getElementById('trialDurationType').required = true;
        document.getElementById('trialDuration').required = true;
        document.getElementById('trialPrice').required = true;
        document.getElementById('billedEvery').required = false;
    } else {
        document.getElementById('trialFields').style.display = 'none';

        // Remove required attributes
        document.getElementById('trialDurationType').required = false;
        document.getElementById('trialDuration').required = false;
        document.getElementById('trialPrice').required = false;
    }
}

function handleSubscriptionChange(checkbox) {
    console.log('Subscription changed (inline):', checkbox.checked);
    if (checkbox.checked) {
        document.getElementById('subscriptionFields').style.display = 'block';
        document.getElementById('trialFields').style.display = 'none';
        document.getElementById('isFreeTrial').checked = false;

        // Set required attributes
        document.getElementById('billedEvery').required = true;
        document.getElementById('trialDurationType').required = false;
        document.getElementById('trialDuration').required = false;
        document.getElementById('trialPrice').required = false;
    } else {
        document.getElementById('subscriptionFields').style.display = 'none';
        document.getElementById('billedEvery').required = false;
    }
}

$(document).ready(function() {
    console.log('Product modal script loaded');

    // Handle image preview
    $('#productImage').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#imagePreview').attr('src', e.target.result).show();
                $('#imagePlaceholder').hide();
            };
            reader.readAsDataURL(file);
        } else {
            $('#imagePreview').hide();
            $('#imagePlaceholder').show();
        }
    });

    // Handle free trial checkbox
    $('#isFreeTrial').on('change', function() {
        console.log('Free trial checkbox changed:', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#trialFields').show();
            $('#subscriptionFields').hide();
            $('#isSubscription').prop('checked', false);
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', true);
            $('#billedEvery').prop('required', false);
            console.log('Trial fields shown');
        } else {
            $('#trialFields').hide();
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
            console.log('Trial fields hidden');
        }
    });

    // Handle subscription checkbox
    $('#isSubscription').on('change', function() {
        console.log('Subscription checkbox changed:', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#subscriptionFields').show();
            $('#trialFields').hide();
            $('#isFreeTrial').prop('checked', false);
            $('#billedEvery').prop('required', true);
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
            console.log('Subscription fields shown');
        } else {
            $('#subscriptionFields').hide();
            $('#billedEvery').prop('required', false);
            console.log('Subscription fields hidden');
        }
    });

    // Handle form submission
    $('#addProductForm').on('submit', function(e) {
        e.preventDefault();

        // Custom validation
        let isValid = true;

        // Check if free trial is selected and required fields are filled
        if ($('#isFreeTrial').is(':checked')) {
            if (!$('#trialDurationType').val()) {
                $('#trialDurationType').addClass('is-invalid');
                $('#trialDurationType').siblings('.invalid-feedback').text('{{ __("Trial Duration Type is required") }}');
                isValid = false;
            }
            if (!$('#trialDuration').val()) {
                $('#trialDuration').addClass('is-invalid');
                $('#trialDuration').siblings('.invalid-feedback').text('{{ __("Trial Duration is required") }}');
                isValid = false;
            }
        }

        // Check if subscription is selected and required fields are filled
        if ($('#isSubscription').is(':checked')) {
            if (!$('#billedEvery').val()) {
                $('#billedEvery').addClass('is-invalid');
                $('#billedEvery').siblings('.invalid-feedback').text('{{ __("Billed Every is required") }}');
                isValid = false;
            }
        }

        if (!isValid) {
            show_toastr('error', '{{ __("Please fill in all required fields") }}');
            return;
        }

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();

        submitBtn.prop('disabled', true).text('{{ __("Creating...") }}');

        $.ajax({
            url: '{{ route("finance.plan.store-product") }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // Show success message
                    show_toastr('success', response.message);

                    // Close modal
                    $('#addProductModal').modal('hide');

                    // Reset form
                    $('#addProductForm')[0].reset();
                    $('#imagePreview').hide();
                    $('#imagePlaceholder').show();
                    $('#trialFields').hide();
                    $('#subscriptionFields').hide();

                    // Reload the page or update the products table
                    location.reload();
                } else {
                    show_toastr('error', response.message || '{{ __("Something went wrong") }}');
                }
            },
            error: function(xhr) {
                let errorMessage = '{{ __("Something went wrong") }}';

                // Clear previous validation errors
                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                if (xhr.status === 422 && xhr.responseJSON && xhr.responseJSON.errors) {
                    // Handle validation errors
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        const input = $(`[name="${field}"]`);
                        const feedback = input.siblings('.invalid-feedback');

                        input.addClass('is-invalid');
                        if (feedback.length) {
                            feedback.text(errors[field][0]);
                        }
                    });
                    errorMessage = '{{ __("Please check the form for errors") }}';
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Clear validation errors when user starts typing
    $('#addProductForm input, #addProductForm select, #addProductForm textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });

    // Reset modal when closed
    $('#addProductModal').on('hidden.bs.modal', function() {
        $('#addProductForm')[0].reset();
        $('#imagePreview').hide();
        $('#imagePlaceholder').show();
        $('#trialFields').hide();
        $('#subscriptionFields').hide();
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').text('');
    });

    // Debug when modal is shown
    $('#addProductModal').on('shown.bs.modal', function() {
        console.log('Modal shown');
        console.log('Free trial checkbox exists:', $('#isFreeTrial').length);
        console.log('Subscription checkbox exists:', $('#isSubscription').length);
        console.log('Trial fields exists:', $('#trialFields').length);
        console.log('Subscription fields exists:', $('#subscriptionFields').length);
    });

    // Alternative event delegation approach in case elements are not ready
    $(document).on('change', '#isFreeTrial', function() {
        console.log('Free trial checkbox changed (delegated):', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#trialFields').show();
            $('#subscriptionFields').hide();
            $('#isSubscription').prop('checked', false);
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', true);
            $('#billedEvery').prop('required', false);
        } else {
            $('#trialFields').hide();
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
        }
    });

    $(document).on('change', '#isSubscription', function() {
        console.log('Subscription checkbox changed (delegated):', $(this).is(':checked'));
        if ($(this).is(':checked')) {
            $('#subscriptionFields').show();
            $('#trialFields').hide();
            $('#isFreeTrial').prop('checked', false);
            $('#billedEvery').prop('required', true);
            $('#trialDurationType, #trialDuration, #trialPrice').prop('required', false);
        } else {
            $('#subscriptionFields').hide();
            $('#billedEvery').prop('required', false);
        }
    });

    // Coupon Modal Functionality
    // Generate random coupon code
    $('#generateCodeBtn').on('click', function() {
        const randomCode = 'COUPON' + Math.random().toString(36).substr(2, 6).toUpperCase();
        $('#couponCode').val(randomCode);
    });

    // Validate discount based on type
    $('#discountType, #discountAmount').on('change input', function() {
        const discountType = $('#discountType').val();
        const discountAmount = parseFloat($('#discountAmount').val());

        if (discountType === 'percentage' && discountAmount > 100) {
            $('#discountAmount').addClass('is-invalid');
            $('#discountAmount').siblings('.invalid-feedback').text('{{ __("Percentage discount cannot exceed 100%") }}');
        } else {
            $('#discountAmount').removeClass('is-invalid');
            $('#discountAmount').siblings('.invalid-feedback').text('');
        }
    });

    // Validate date range
    $('#startDate, #endDate').on('change', function() {
        const startDate = $('#startDate').val();
        const endDate = $('#endDate').val();

        if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
            $('#endDate').addClass('is-invalid');
            $('#endDate').siblings('.invalid-feedback').text('{{ __("End date must be after start date") }}');
        } else {
            $('#endDate').removeClass('is-invalid');
            $('#endDate').siblings('.invalid-feedback').text('');
        }
    });

    // Handle coupon form submission (both create and update)
    $('#addCouponForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();
        const isEdit = $('#editCouponId').length > 0;
        const url = isEdit ? '{{ route("finance.plan.update-coupon") }}' : '{{ route("finance.plan.store-coupon") }}';

        submitBtn.prop('disabled', true).text(isEdit ? '{{ __("Updating...") }}' : '{{ __("Creating...") }}');

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    show_toastr('success', response.message);
                    $('#addCouponModal').modal('hide');
                    location.reload();
                } else {
                    show_toastr('error', response.message || '{{ __("Something went wrong") }}');
                }
            },
            error: function(xhr) {
                let errorMessage = '{{ __("Something went wrong") }}';

                $('.form-control').removeClass('is-invalid');
                $('.invalid-feedback').text('');

                if (xhr.status === 422 && xhr.responseJSON?.errors) {
                    const errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(field) {
                        const input = $(`[name="${field}"]`);
                        const feedback = input.closest('.mb-3').find('.invalid-feedback');
                        input.addClass('is-invalid');
                        feedback.text(errors[field][0]);
                    });
                    errorMessage = '{{ __("Please check the form for errors") }}';
                } else if (xhr.responseJSON?.error) {
                    errorMessage = xhr.responseJSON.error;
                } else if (xhr.responseJSON?.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                show_toastr('error', errorMessage);
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });


    // Clear validation errors when user starts typing
    $('#addCouponForm input, #addCouponForm select, #addCouponForm textarea').on('input change', function() {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').text('');
    });

    // Reset modal when closed
    $('#addCouponModal').on('hidden.bs.modal', function() {
        $('#addCouponForm')[0].reset();
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').text('');

        // Reset modal to create mode
        $('#addCouponModalLabel').text("{{ __('Create Coupon') }}");
        $('#addCouponForm button[type=submit]').text("{{ __('Create Coupon') }}");
        $('#editCouponId').remove();
    });

    // Generate coupon
    $(".generate_coupon").on('click', function(e) {
        e.preventDefault();

        var name = $('#couponName').val().toUpperCase().replace(/\s+/g, '');

        if (name.length < 3) {
            alert("Enter a longer coupon name to generate a valid code.");
            return;
        }

        // Append 3 random digits to the name
        var randomDigits = Math.floor(100 + Math.random() * 900); // generates a number between 100-999
        var finalCode = name + randomDigits;

        $('#couponCode').val(finalCode);
    });

    // Edit Coupon Event
    $(document).on('click', '#editCoupon', function () {
        var couponId = $(this).data('coupon-id');
        $.ajax({
            url: '{{ route("finance.plan.edit-coupon") }}', // Route should return coupon data
            type: 'GET',
            data: {
                coupon_id: couponId
            },
            success: function (response) {
                if (response.success) {
                    const coupon = response.data;

                    // Set modal fields
                    $('#couponName').val(coupon.name);
                    $('#couponCode').val(coupon.code);
                    $('#couponProduct').val(coupon.product_id);
                    $('#discountType').val(coupon.discount_type);
                    $('#discountAmount').val(coupon.discount);
                    $('#usageLimit').val(coupon.limit);
                    $('#startDate').val(coupon.start_date);
                    $('#endDate').val(coupon.end_date);
                    $('#isActive').prop('checked', coupon.is_active == 1);
                    $('#couponDescription').val(coupon.description);

                    // Change modal UI for edit
                    $('#addCouponModalLabel').text("{{ __('Edit Coupon') }}");
                    $('#addCouponForm button[type=submit]').text("{{ __('Update Coupon') }}");

                    // Add hidden input to track edit
                    $('#addCouponForm').append(`<input type="hidden" name="coupon_id" id="editCouponId" value="${coupon.id}">`);

                    // Show modal
                    $('#addCouponModal').modal('show');
                } else {
                    show_toastr('error', response.message || 'Failed to load coupon');
                }
            },
            error: function () {
                show_toastr('error', 'Failed to load coupon');
            }
        });
    });

    // View Coupon Event
    $(document).on('click', '#viewCoupon', function () {
        var couponId = $(this).data('coupon-id');
        $.ajax({
            url: '{{ route("finance.plan.edit-coupon") }}',
            type: 'GET',
            data: {
                coupon_id: couponId
            },
            success: function (response) {
                if (response.success) {
                    const coupon = response.data;

                    // Create view modal content
                    const viewContent = `
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">{{ __('Coupon Name') }}</label>
                                <p class="form-control-plaintext">${coupon.name}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">{{ __('Coupon Code') }}</label>
                                <p class="form-control-plaintext"><code>${coupon.code}</code></p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">{{ __('Product') }}</label>
                                <p class="form-control-plaintext">${coupon.product_id ? $('#couponProduct option[value="' + coupon.product_id + '"]').text() : '{{ __("All Products") }}'}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">{{ __('Discount Type') }}</label>
                                <p class="form-control-plaintext">${coupon.discount_type === 'percentage' ? '{{ __("Percentage") }}' : '{{ __("Fixed Amount") }}'}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">{{ __('Discount Amount') }}</label>
                                <p class="form-control-plaintext">${coupon.discount}${coupon.discount_type === 'percentage' ? '%' : ''}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">{{ __('Usage Limit') }}</label>
                                <p class="form-control-plaintext">${coupon.limit}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">{{ __('Start Date') }}</label>
                                <p class="form-control-plaintext">${coupon.start_date || '{{ __("No Start Date") }}'}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">{{ __('End Date') }}</label>
                                <p class="form-control-plaintext">${coupon.end_date || '{{ __("No End Date") }}'}</p>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">{{ __('Status') }}</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-${coupon.is_active ? 'success' : 'danger'}">
                                        ${coupon.is_active ? '{{ __("Active") }}' : '{{ __("Inactive") }}'}
                                    </span>
                                </p>
                            </div>
                            <div class="col-12 mb-3">
                                <label class="form-label fw-bold">{{ __('Description') }}</label>
                                <p class="form-control-plaintext">${coupon.description || '{{ __("No description") }}'}</p>
                            </div>
                        </div>
                    `;

                    // Show view modal
                    $('#viewCouponModal .modal-body').html(viewContent);
                    $('#viewCouponModal').modal('show');
                } else {
                    show_toastr('error', response.message || 'Failed to load coupon');
                }
            },
            error: function () {
                show_toastr('error', 'Failed to load coupon');
            }
        });
    });

    // Delete Coupon Event
    $(document).on('click', '#deleteCoupon', function () {
        var couponId = $(this).data('coupon-id');

        if (confirm('{{ __("Are you sure you want to delete this coupon?") }}')) {
            $.ajax({
                url: '{{ route("finance.plan.delete-coupon") }}',
                type: 'POST',
                data: {
                    coupon_id: couponId,
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function (response) {
                    if (response.success) {
                        show_toastr('success', response.message);
                        location.reload();
                    } else {
                        show_toastr('error', response.message || 'Failed to delete coupon');
                    }
                },
                error: function () {
                    show_toastr('error', 'Failed to delete coupon');
                }
            });
        }
    });
});
</script>
